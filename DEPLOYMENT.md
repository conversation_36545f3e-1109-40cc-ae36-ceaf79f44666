# CNInfo 前端打包部署教程

## 环境要求

- Node.js 18+ 
- npm 或 yarn
- Caddy 2.x

## 1. 前端打包

### 1.1 安装依赖

```bash
cd frontend
npm install
```

如果网络较慢，可以使用淘宝镜像：

```bash
npm config set registry https://registry.npmmirror.com
npm install
```

### 1.2 环境变量配置

在 `frontend` 目录下创建 `.env.production` 文件：

```bash
# API 后端地址
VITE_API_URL=http://localhost:5000
```

### 1.3 构建生产版本

```bash
npm run build
```

构建完成后，会在 `frontend/dist` 目录生成静态文件。

## 2. Caddy 配置

### 2.1 安装 Caddy

**Ubuntu/Debian:**
```bash
sudo apt install -y debian-keyring debian-archive-keyring apt-transport-https
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | sudo gpg --dearmor -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | sudo tee /etc/apt/sources.list.d/caddy-stable.list
sudo apt update
sudo apt install caddy
```

**CentOS/RHEL:**
```bash
dnf install 'dnf-command(copr)'
dnf copr enable @caddy/caddy
dnf install caddy
```

**macOS:**
```bash
brew install caddy
```

**Windows:**
下载 [Caddy 二进制文件](https://caddyserver.com/download)

### 2.2 创建 Caddyfile

在项目根目录创建 `Caddyfile`：

```caddy
# 主站点配置
:80 {
    # 静态文件服务
    root * ./frontend/dist
    file_server
    
    # API 反向代理
    handle /api/* {
        reverse_proxy localhost:5000
    }
    
    # SPA 路由支持
    try_files {path} /index.html
    
    # 启用压缩
    encode gzip
    
    # 静态资源缓存
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"
    
    # 日志配置
    log {
        output file /var/log/caddy/access.log
        format json
    }
}
```

### 2.3 HTTPS 配置（可选）

如果需要 HTTPS，修改 Caddyfile：

```caddy
your-domain.com {
    root * ./frontend/dist
    file_server
    
    handle /api/* {
        reverse_proxy localhost:5000
    }
    
    try_files {path} /index.html
    encode gzip
    
    @static {
        path *.js *.css *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2
    }
    header @static Cache-Control "public, max-age=31536000"
    
    log {
        output file /var/log/caddy/access.log
        format json
    }
}
```

## 3. 部署脚本

### 3.1 创建部署脚本

创建 `deploy.sh`：

```bash
#!/bin/bash

echo "开始部署 CNInfo 前端..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "错误: 未安装 Node.js"
    exit 1
fi

# 检查 Caddy
if ! command -v caddy &> /dev/null; then
    echo "错误: 未安装 Caddy"
    exit 1
fi

# 进入前端目录
cd frontend

# 安装依赖
echo "安装依赖..."
npm install

# 构建项目
echo "构建项目..."
npm run build

if [ $? -ne 0 ]; then
    echo "错误: 构建失败"
    exit 1
fi

# 返回项目根目录
cd ..

# 创建日志目录
sudo mkdir -p /var/log/caddy
sudo chown $USER:$USER /var/log/caddy

# 启动 Caddy
echo "启动 Caddy 服务器..."
caddy start --config Caddyfile

echo "部署完成！"
echo "前端访问地址: http://localhost"
echo "API 地址: http://localhost/api"
```

给脚本执行权限：

```bash
chmod +x deploy.sh
```

### 3.2 创建停止脚本

创建 `stop.sh`：

```bash
#!/bin/bash

echo "停止 Caddy 服务器..."
caddy stop

echo "服务已停止"
```

给脚本执行权限：

```bash
chmod +x stop.sh
```

## 4. 系统服务配置（可选）

### 4.1 创建 systemd 服务

创建 `/etc/systemd/system/cninfo-caddy.service`：

```ini
[Unit]
Description=CNInfo Caddy Server
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
ExecStart=/usr/bin/caddy run --config /path/to/your/project/Caddyfile
ExecReload=/usr/bin/caddy reload --config /path/to/your/project/Caddyfile
TimeoutStopSec=5s
LimitNOFILE=1048576
LimitNPROC=1048576
PrivateTmp=true
ProtectSystem=full
AmbientCapabilities=CAP_NET_BIND_SERVICE

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable cninfo-caddy
sudo systemctl start cninfo-caddy
```

## 5. 部署步骤

### 5.1 完整部署流程

1. **准备环境**
   ```bash
   # 克隆项目
   git clone <your-repo>
   cd cninfo_process
   ```

2. **配置环境变量**
   ```bash
   # 前端环境变量
   echo "VITE_API_URL=http://localhost:5000" > frontend/.env.production
   ```

3. **执行部署**
   ```bash
   ./deploy.sh
   ```

4. **启动后端服务**
   ```bash
   cd backend
   python app.py
   ```

### 5.2 验证部署

访问 `http://localhost` 检查前端是否正常加载。

检查 API 是否正常：
```bash
curl http://localhost/api/health
```

## 6. 常见问题

### 6.1 端口冲突

如果 80 端口被占用，修改 Caddyfile：

```caddy
:8080 {
    # ... 其他配置
}
```

### 6.2 权限问题

确保 Caddy 有权限访问静态文件：

```bash
sudo chown -R www-data:www-data frontend/dist
```

### 6.3 API 代理失败

检查后端服务是否运行：

```bash
curl http://localhost:5000/api/health
```

### 6.4 静态文件 404

确保构建文件存在：

```bash
ls -la frontend/dist/
```

## 7. 性能优化

### 7.1 启用 HTTP/2

在 Caddyfile 中添加：

```caddy
:443 {
    # 自动启用 HTTP/2
    protocols h1 h2
    # ... 其他配置
}
```

### 7.2 启用 Brotli 压缩

```caddy
encode {
    gzip
    zstd
}
```

### 7.3 设置安全头

```caddy
header {
    X-Content-Type-Options nosniff
    X-Frame-Options DENY
    X-XSS-Protection "1; mode=block"
    Strict-Transport-Security "max-age=31536000; includeSubDomains"
}
```

## 8. 监控和日志

### 8.1 查看访问日志

```bash
tail -f /var/log/caddy/access.log
```

### 8.2 查看 Caddy 状态

```bash
caddy list-adapters
caddy version
```

### 8.3 重新加载配置

```bash
caddy reload --config Caddyfile
```

部署完成后，您的 CNInfo 应用将通过 Caddy 提供高性能的静态文件服务和 API 反向代理。
